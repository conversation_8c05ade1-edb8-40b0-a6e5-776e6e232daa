<template>
    <div>
        <Container>
            <template #head>
                <div class="ip_head">民事一审
                    <Steps :active="3" />
                </div>
            </template>
            <div class="con">
                {{ $store.getters.lafsName }}-{{ $store.getters.applyCaseTypeName}}
                <div style="display: flex">
                    <div class="border left_con">
                        <div v-for="(tab,index) of tabs" class="tab"
                            :class="{active: activeTab == index,require: requiredTabs.includes(tab),disabled: tab=='代理人信息' && dsrMap[1] && dsrMap[1].length > 0,undone: !isDoneMap[index]}"
                            @click="activeTab = index" :key="tab">{{tab}}</div>
                    </div>
                    <div class="border right_con" v-if="activeTab == 0">
                        <FormItem label="标的金额(元)" :required="true">
                            <input v-model="layyInfo.sqbdje" placeholder="请输入" />
                        </FormItem>
                    </div>
                    <div class="right_con" v-if="activeTab == 1 || activeTab == 2 || activeTab ==3">
                        <div class="con_head">
                            <div class="right_tabs">
                                <div v-for="dsr of dsrs" :key="dsr" class="right_tab"
                                    :class="{active: activeDsr == dsr.index,undone: undoneStatus[dsr.index]}"
                                    @click="activeDsr = dsr.index" :ref="`dsrtab_${dsr.index}`">
                                    <span>{{$getText(codes.dsrZysar,dsr.dsrlx)}}</span>
                                </div>
                            </div>
                            <div class="r_btns">
                                <div class="r_btn" @click="addDsr(dsrCode)" v-for="dsrCode of codes.dsrZysar"
                                    :key="dsrCode.value">+{{dsrCode.text}}</div>
                            </div>
                        </div>
                        <div class="con_body" v-if="allDsrs[activeDsr] && allDsrs[activeDsr].dsrlx == '1501_000011-1'">
                            <FormItem label="当事人类型" :required="true">
                                <CustomSelect v-model="allDsrs[activeDsr].dsrlx" :list="codes.dsrZysar"
                                    :filterable="true">
                                    <div class="selectDiv center">
                                        {{$getText(codes.dsrZysar,allDsrs[activeDsr].dsrlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="姓名" :required="true">
                                <input v-model="allDsrs[activeDsr].xm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="性别" :required="true">
                                <CustomSelect v-model="allDsrs[activeDsr].xb" :list="codes.sex">
                                    <div class="selectDiv center">
                                        {{$getText(codes.sex,allDsrs[activeDsr].xb)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="国别或地区" :required="true">
                                <CustomSelect v-model="allDsrs[activeDsr].gj" :list="codes.gj" :filterable="true"
                                    :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.gj,allDsrs[activeDsr].gj)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="证件类型" :required="activeTab == 1">
                                <CustomSelect v-model="allDsrs[activeDsr].zjlx" :list="codes.sfzjlx" :filterable="true"
                                    :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.sfzjlx,allDsrs[activeDsr].zjlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="证件号码" :required="activeTab == 1">
                                <input v-model="allDsrs[activeDsr].zjhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="出生日期">
                                <DateInput v-model="allDsrs[activeDsr].csrq" maxlength="10" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="年龄">
                                <input v-model="allDsrs[activeDsr].nl" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="民族">
                                <CustomSelect v-model="allDsrs[activeDsr].mz" :list="codes.mz" :filterable="true">
                                    <div class="selectDiv center">
                                        {{$getText(codes.mz,allDsrs[activeDsr].mz)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="职业">
                                <CustomSelect v-model="allDsrs[activeDsr].zy" :list="codes.zy" :filterable="true"
                                    :width="500">
                                    <div class="selectDiv center">
                                        {{$getText(codes.zy,allDsrs[activeDsr].zy)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="工作单位" :span="2">
                                <input v-model="allDsrs[activeDsr].gzdw" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="手机号码" :required="activeTab == 1">
                                <input v-model="allDsrs[activeDsr].sjhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="住所地址" :span="2" :required="true">
                                <input v-model="allDsrs[activeDsr].dz" placeholder="请输入" />
                            </FormItem>
                            <div class="account_png">
                                <div class="addPng center" @click="openAddressModal" v-show="activeTab == 1">
                                    {{allDsrs[activeDsr].skzhxxId?'已添加收款账户确认书' : '+添加收款账户确认书'}}
                                </div>
                                <div class="delPng" @click="delPng(allDsrs[activeDsr].skzhxxId,allDsrs[activeDsr])"
                                    v-if="allDsrs[activeDsr].skzhxxId">删除</div>
                            </div>

                        </div>
                        <div class="con_body"
                            v-else-if="allDsrs[activeDsr] && allDsrs[activeDsr].dsrlx == '1501_000011-2'">
                            <FormItem label="当事人类型" :required="true">
                                <CustomSelect v-model="allDsrs[activeDsr].dsrlx" :list="codes.dsrZysar"
                                    :filterable="true">
                                    <div class="selectDiv center">
                                        {{$getText(codes.dsrZysar,allDsrs[activeDsr].dsrlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="单位名称" :required="true">
                                <input v-model="allDsrs[activeDsr].dwmc" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="单位所在地" :required="true">
                                <input v-model="allDsrs[activeDsr].dwzsd" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="国别或地区" :required="true">
                                <CustomSelect v-model="allDsrs[activeDsr].gj" :list="codes.gj" :filterable="true"
                                    :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.gj,allDsrs[activeDsr].gj)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="证照类型" :required="activeTab == 1">
                                <CustomSelect v-model="allDsrs[activeDsr].zzlx" :list="codes.zzlx" :filterable="false"
                                    :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.zzlx,allDsrs[activeDsr].zzlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="证照号码" :required="activeTab == 1">
                                <input v-model="allDsrs[activeDsr].zzhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="法人代表姓名">
                                <input v-model="allDsrs[activeDsr].fddbrxm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="法人代表职务">
                                <input v-model="allDsrs[activeDsr].fddbrzw" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="法人代表证件类型">
                                <CustomSelect v-model="allDsrs[activeDsr].fddbrzjlx" :list="codes.sfzjlx"
                                    :filterable="true" :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.sfzjlx,allDsrs[activeDsr].fddbrzjlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="法人代表证件号">
                                <input v-model="allDsrs[activeDsr].fddbrzjhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="法人代表手机号" :required="activeTab == 1">
                                <input v-model="allDsrs[activeDsr].fddbrsjhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="法人代表固定电话">
                                <input v-model="allDsrs[activeDsr].fddbrgddh" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="单位性质">
                                <CustomSelect v-model="allDsrs[activeDsr].dwxz" :list="codes.dwxz" :filterable="true">
                                    <div class="selectDiv center">
                                        {{$getText(codes.dwxz,allDsrs[activeDsr].dwxz)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <div class="account_png">
                                <div class="addPng center" @click="openAddressModal" v-show="activeTab == 1">
                                    {{allDsrs[activeDsr].skzhxxId?'已添加收款账户确认书' : '+添加收款账户确认书'}}
                                </div>
                                <div class="delPng" @click="delPng(allDsrs[activeDsr].skzhxxId,allDsrs[activeDsr])"
                                    v-if="allDsrs[activeDsr].skzhxxId">删除</div>
                            </div>

                        </div>
                        <div class="con_body"
                            v-else-if="allDsrs[activeDsr] && allDsrs[activeDsr].dsrlx == '1501_000011-3'">
                            <FormItem label="当事人类型" :required="true">
                                <CustomSelect v-model="allDsrs[activeDsr].dsrlx" :list="codes.dsrZysar"
                                    :filterable="true">
                                    <div class="selectDiv center">
                                        {{$getText(codes.dsrZysar,allDsrs[activeDsr].dsrlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="单位名称" :required="true">
                                <input v-model="allDsrs[activeDsr].dwmc" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="单位所在地" :required="true">
                                <input v-model="allDsrs[activeDsr].dwzsd" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="国别或地区" :required="true">
                                <CustomSelect v-model="allDsrs[activeDsr].gj" :list="codes.gj" :filterable="true"
                                    :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.gj,allDsrs[activeDsr].gj)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="证照类型" :required="activeTab == 1">
                                <CustomSelect v-model="allDsrs[activeDsr].zzlx" :list="codes.zzlx" :filterable="false"
                                    :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.zzlx,allDsrs[activeDsr].zzlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="证照号码" :required="activeTab == 1">
                                <input v-model="allDsrs[activeDsr].zzhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="主要负责人姓名">
                                <input v-model="allDsrs[activeDsr].fddbrxm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="主要负责人职务">
                                <input v-model="allDsrs[activeDsr].fddbrzw" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="负责人证件类型">
                                <CustomSelect v-model="allDsrs[activeDsr].fddbrzjlx" :list="codes.sfzjlx"
                                    :filterable="true" :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.sfzjlx,allDsrs[activeDsr].fddbrzjlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="负责人证件号码">
                                <input v-model="allDsrs[activeDsr].fddbrzjhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="负责人手机号码" :required="activeTab == 1">
                                <input v-model="allDsrs[activeDsr].fddbrsjhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="负责人固定电话">
                                <input v-model="allDsrs[activeDsr].fddbrgddh" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="单位性质">
                                <CustomSelect v-model="allDsrs[activeDsr].dwxz" :list="codes.dwxz" :filterable="true">
                                    <div class="selectDiv center">
                                        {{$getText(codes.dwxz,allDsrs[activeDsr].dwxz)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <div class="account_png">
                                <div class="addPng center" @click="openAddressModal" v-show="activeTab == 1">
                                    {{allDsrs[activeDsr].skzhxxId?'已添加收款账户确认书' : '+添加收款账户确认书'}}
                                </div>
                                <div class="delPng" @click="delPng(allDsrs[activeDsr].skzhxxId,allDsrs[activeDsr])"
                                    v-if="allDsrs[activeDsr].skzhxxId">删除</div>
                            </div>

                        </div>
                        <div class="con_body" v-else>
                            <div class="empty_body center">请添加{{tabs[activeTab]}}</div>
                        </div>
                        <img :src="delCircle" v-if="activeDsr != null && (!isBenren || activeDsr != 0)" @click="delDsr"
                            class="delDsr" />
                    </div>
                    <div class="right_con" v-if="activeTab == 4">
                        <div class="con_head">
                            <div class="right_tabs">
                                <div v-for="(dlr,index) of dlrs" :key="dlr" class="right_tab"
                                    :class="{active: activeDlr == index,undone: undoneDlrStatus[index]}"
                                    @click="activeDlr = index" :ref="`dlrtab_${index}`">
                                    <span>代理人{{index + 1}}</span>
                                </div>
                            </div>
                            <div class="r_btns">
                                <div class="r_btn" @click="addDlr()">+代理人</div>
                            </div>
                        </div>
                        <div class="con_body" v-if="activeDlrObj" style="border: 1px solid #a1b1c5;height:505px">
                            <FormItem label="被代理人" :required="true">
                                <CustomSelect v-model="activeDlrObj.bdlrid" :list="ygDsrs" :filterable="true">
                                    <div class="selectDiv center">
                                        {{$getText(ygDsrs,activeDlrObj.bdlrid)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="代理人类型" :required="true" :span="2">
                                <CustomSelect v-model="activeDlrObj.dlrlx" :list="codes.ssdlrlx" :filterable="true"
                                    :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.ssdlrlx,activeDlrObj.dlrlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="代理人姓名" :required="true">
                                <input v-model="activeDlrObj.xm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="代理人证件类型" :required="true">
                                <CustomSelect v-model="activeDlrObj.zjlx" :list="codes.sfzjlx" :filterable="true"
                                    :width="400">
                                    <div class="selectDiv center">
                                        {{$getText(codes.sfzjlx,activeDlrObj.zjlx)}}
                                    </div>
                                </CustomSelect>
                            </FormItem>
                            <FormItem label="代理人证件号" :required="true">
                                <input v-model="activeDlrObj.zjhm" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="代理人执业证号" :required="true"
                                v-if="activeDlrObj.dlrlx == '1501_000013-1' || activeDlrObj.dlrlx == '1501_000013-2'">
                                <input v-model="activeDlrObj.zyzh" placeholder="请输入" />
                            </FormItem>
                            <FormItem label="代理人执业机构"
                                v-if="activeDlrObj.dlrlx == '1501_000013-1' || activeDlrObj.dlrlx == '1501_000013-2'">
                                <input v-model="activeDlrObj.zyjg" placeholder="请输入" />
                            </FormItem>

                            <FormItem label="代理人手机号" :required="true">
                                <input v-model="activeDlrObj.sjhm" placeholder="请输入" />
                            </FormItem>

                        </div>
                        <div class="con_body" v-else>
                            <div class="empty_body center">请添加代理人信息</div>
                        </div>
                        <img :src="delCircle" v-if="activeDlrObj != null && (isBenren || activeDlr != 0)"
                            @click="delDlr" class="delDsr" />
                    </div>
                </div>
                <AddressModal v-model:open="addressModalOpen" @confirm="confirmAddress" v-if="allDsrs[activeDsr]" />
                <div class="btns">
                    <div class="ip_btn" @click="goBack">上一步</div>
                    <div class="ip_btn deep_btn" @click="goNext" v-if="isAllDone">下一步</div>
                </div>
            </div>
        </Container>
        <!-- 图片预览蒙层 -->
        <div v-if="showPreview" class="preview-overlay" @click.self="closePreview">
            <div class="preview-container">
                <img :src="previewImage" alt="预览图片" class="preview-image">
            </div>
        </div>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import Steps from '@/components/Steps.vue'
import FormItem from '@/components/FormItem.vue'
import DateInput from '@/components/DateInput.vue'
import CustomSelect from '@/components/CustomSelect.vue'
import AddressModal from '@/components/AddressModal.vue'
import delCircle from '@/assets/del_circle.png'
import { APPLY_USER_TYPE } from '@/utils/bizConstant'
export default {
    components: {
        Container,
        Steps,
        FormItem,
        DateInput,
        CustomSelect,
        AddressModal,
    },
    data() {
        return {
            delCircle: delCircle,
            tabs: [
                '标的信息',
                '原告信息',
                '被告信息',
                '第三人信息',
                '代理人信息',
            ],
            requiredTabs: ['标的信息', '原告信息', '被告信息'],
            activeTab: 0,

            allDsrs: [],
            savedDsrs: [], //保存后的用户信息，用于判断是否修改
            dlrs: [],
            savedDlrs: [],
            activeDsr: 0,
            activeDlr: null,
            requiredProps: {
                '1501_000011-1': {
                    1: [
                        'dsrlx',
                        'xm',
                        'xb',
                        'gj',
                        'zjlx',
                        'zjhm',
                        'dz',
                        'sjhm',
                    ],
                    2: ['dsrlx', 'xm', 'xb', 'gj', 'dz'],
                    3: ['dsrlx', 'xm', 'xb', 'gj', 'dz'],
                },
                '1501_000011-2': {
                    1: [
                        'dsrlx',
                        'dwmc',
                        'dwzsd',
                        'gj',
                        'zzlx',
                        'zzhm',
                        'fddbrsjhm',
                    ],
                    2: ['dsrlx', 'dwmc', 'dwzsd', 'gj'],
                    3: ['dsrlx', 'dwmc', 'dwzsd', 'gj'],
                },
                '1501_000011-3': {
                    1: [
                        'dsrlx',
                        'dwmc',
                        'dwzsd',
                        'gj',
                        'zzlx',
                        'zzhm',
                        'fddbrsjhm',
                    ],
                    2: ['dsrlx', 'dwmc', 'dwzsd', 'gj'],
                    3: ['dsrlx', 'dwmc', 'dwzsd', 'gj'],
                },
                '1501_000013-1': [
                    'bdlrid',
                    'xm',
                    'zjlx',
                    'zjhm',
                    'zyzh',
                    'sjhm',
                ],
                '1501_000013-2': [
                    'bdlrid',
                    'xm',
                    'zjlx',
                    'zjhm',
                    'zyzh',
                    'sjhm',
                ],
                '1501_000013-3': ['bdlrid', 'xm', 'zjlx', 'zjhm', 'sjhm'],
                '1501_000013-4': ['bdlrid', 'xm', 'zjlx', 'zjhm', 'sjhm'],
                '1501_000013-5': ['bdlrid', 'xm', 'zjlx', 'zjhm', 'sjhm'],
            },
            ssdws: {
                1: '1501_030109-1',
                2: '1501_030109-2',
                3: '1501_030109-5',
            },
            ssdwList: ['', '1501_030109-1', '1501_030109-2', '1501_030109-5'],
            codes: {},
            addressModalOpen: false,

            // 是否已完成
            isDoneMap: {
                0: true,
                1: true,
                2: true,
                3: true,
                4: true,
            },
            layyid: this.$store.getters.layyid,
            layyInfo: { sqbdje: '' }, // 标的金额,
            sscls: [], // 诉讼材料列表

            showPreview: false,
            previewImage: false,
        }
    },
    async mounted() {
        if (localStorage.getItem('codes')) {
            this.codes = JSON.parse(localStorage.getItem('codes'))
        } else {
            let res = await this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/code',
                { method: 'GET' }
            )
            this.codes = res.data
            localStorage.setItem('codes', JSON.stringify(res.data))
        }
        this.getBaseInfo()
    },
    watch: {
        // 监听active,当变化时，获取变化前对应的dsr信息，判断其undone
        async activeDsr(newVal, oldVal) {
            if (oldVal == null) {
                return
            }
            let isUnDone = this.undoneStatus[oldVal]
            if (!isUnDone) {
                this.saveDsr(this.allDsrs[oldVal])
            }
            await this.$nextTick()
            if (newVal != null) {
                this.$refs[
                    `dsrtab_${this.allDsrs[newVal].index}`
                ][0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                })
            }
        },
        async activeDlr(newVal, oldVal) {
            if (oldVal == null) {
                return
            }
            let isUnDone = this.undoneDlrStatus[oldVal]
            if (!isUnDone) {
                this.saveDlr(this.dlrs[oldVal])
            }
            await this.$nextTick()
            if (newVal != null) {
                this.$refs[`dlrtab_${newVal}`][0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                })
            }
        },
        undoneStatus() {
            this.checkIsDone()
        },
        undoneDlrStatus() {
            this.checkIsDone()
        },
        sqbdje() {
            this.checkIsDone()
        },
        // 切换tab时，判断是否undone，
        activeTab(newVal, oldVal) {
            if (this.dsrs && this.dsrs.length) {
                this.activeDsr = this.dsrs[0].index
            } else {
                this.activeDsr = null
            }
            if (oldVal == 4) {
                this.activeDlr = null
            }
            if (newVal == 4 && this.dlrs && this.dlrs.length) {
                this.activeDlr = 0
            }
            this.checkIsDone()
        },
    },
    computed: {
        isBenren() {
            return this.$store.getters.applyUserType == APPLY_USER_TYPE.BENREN
        },
        isAllDone() {
            let isDone = this.checkIsDone()
            if (Object.values(isDone).some((t) => !t)) {
                return false
            }
            return true
        },
        // 为每个 dsr 生成 undone 状态
        undoneStatus() {
            return this.allDsrs.map((dsr) => {
                if (!dsr.dsrlx) {
                    return false
                }
                return this.requiredProps[dsr.dsrlx][
                    this.ssdwList.indexOf(dsr.ssdw)
                ].some((prop) => !dsr[prop])
            })
        },

        undoneDlrStatus() {
            return this.dlrs.map((dlr) => {
                if (!dlr.dlrlx) {
                    return true
                }
                return this.requiredProps[dlr.dlrlx].some((prop) => !dlr[prop])
            })
        },
        dsrMap() {
            let dsrMap = {}
            for (let ssdw of Object.values(this.ssdws)) {
                dsrMap[ssdw] = this.allDsrs.filter((dsr) => dsr.ssdw == ssdw)
            }
            return dsrMap
        },
        dsrs() {
            return this.dsrMap[this.ssdws[this.activeTab]]
        },

        activeDlrObj() {
            return this.dlrs[this.activeDlr]
        },
        // 可用的原告
        ygDsrs() {
            return this.dsrMap['1501_030109-1']
                .filter((d) => d.id)
                .map((d) => {
                    return {
                        ...d,
                        text: d.xm || d.dwmc,
                        value: d.id,
                    }
                })
        },
    },
    methods: {
        goBack() {
            this.$router.push({
                path: '/step3',
                query: {
                    id: this.$store.getters.layyId,
                },
            })
        },
        async goNext() {
            // 校验各个tab是否填写完成

            let isDone = this.checkIsDone()
            if (Object.values(isDone).some((t) => !t)) {
                return
            }
            // 再次保存当前tab中的人 防止修改了直接点下一步
            if ([1, 2, 3].indexOf(this.activeTab) != -1) {
                await this.saveDsr(this.allDsrs[this.activeDsr])
            }
            if (this.activeTab == 4) {
                await this.saveDlr(this.activeDlrObj)
            }

            this.layyInfo.dsrMc = this.allDsrs
                .filter((d) => d.xm || d.dwmc)
                .map((d) => d.xm || d.dwmc)
                .join('、')
            await this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy',
                {
                    method: 'PATCH',
                    body: this.layyInfo,
                }
            )
            this.$router.push('/Step4-1')
        },
        checkIsDone() {
            let isDone = {
                0: this.layyInfo.sqbdje != null && this.layyInfo.sqbdje != '',
                1:
                    this.dsrMap['1501_030109-1'] &&
                    this.dsrMap['1501_030109-1'].length > 0,
                2:
                    this.dsrMap['1501_030109-2'] &&
                    this.dsrMap['1501_030109-2'].length > 0,
                3: true,
                4: !this.undoneDlrStatus.some((t) => t),
            }
            for (let i = 0; i < this.undoneStatus.length; i++) {
                if (this.undoneStatus[i]) {
                    let ssdw = this.allDsrs[i].ssdw
                    isDone[this.ssdwList.indexOf(ssdw)] = false
                }
            }
            this.isDoneMap = isDone
            return isDone
        },
        /**
         * 获取立案基本信息
         */
        async getBaseInfo() {
            let res = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.layyid}/0`,
                {
                    method: 'GET',
                }
            )
            this.layyInfo = res.data.layy
            this.sscls = res.data.sscls
            // 处理当事人
            let dsrs = res.data.dsrs
            if (dsrs)
                for (let dsr of dsrs) {
                    dsr.index = dsrs.indexOf(dsr)
                    try {
                        let skzh = this.sscls
                            .filter((t) => t.clmc == '收款账户确认书')[0]
                            .wjs.filter((t) => t.ssryId == dsr.id)[0]
                        dsr.skzhxxId = skzh.id
                        dsr.url = skzh.url
                    } catch (e) {}
                }
            this.allDsrs = dsrs
            this.savedDsrs = JSON.parse(JSON.stringify(dsrs))

            // 处理代理人
            let dlrs = res.data.dlrs

            this.dlrs = dlrs
            this.savedDlrs = JSON.parse(JSON.stringify(dlrs))
        },

        addDsr(type) {
            let index = this.allDsrs.length
            let item = {
                cdsrlx: type.text,
                dsrlx: type.value,
                ssdw: this.ssdws[this.activeTab],
                index: index,
                layyid: this.layyid,
                csrq: '',
                gj: '1501_GB0006-156', // 中国
            }
            console.log(this.codes.dsrZysar, 'this.codes.dsrZysar')
            if (type.value == this.codes.dsrZysar[0].value) {
                item.zjlx = this.codes.sfzjlx[0].value
            }
            this.allDsrs.push(item)
            this.activeDsr = index
        },

        async saveDsr(dsrInfo) {
            if (!dsrInfo) {
                return
            }

            let index = this.allDsrs.indexOf(dsrInfo)
            let savedDsr = this.savedDsrs[index]
            if (
                savedDsr &&
                JSON.stringify(savedDsr) === JSON.stringify(dsrInfo)
            ) {
                return
            }
            let res = await this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/dsr',
                {
                    method: dsrInfo.id ? 'PATCH' : 'POST',
                    body: {
                        fyId: this.$store.getters.fyId,
                        ...dsrInfo,
                        dwxzMc: this.$getText(this.codes.dwxz, dsrInfo.dwxz),
                        zyMc: this.$getText(this.codes.zy, dsrInfo.zy),
                    },
                }
            )
            if (!this.allDsrs[index].id) this.allDsrs[index].id = res.data
            let skzhxxId = dsrInfo.skzhxxId
            if (this.allDsrs[index].skzhUnSave) {
                this.$rpa
                    .dealFetch(
                        'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/skzhxx',
                        {
                            method: 'POST',
                            body: {
                                dsrId: this.allDsrs[index].id,
                                layyId: this.layyid,
                                fyid: this.$store.getters.fyId,
                                dsrmc: dsrInfo.xm || dsrInfo.dwmc,
                                skzhxxId: skzhxxId,
                                ssclid: this.sscls.filter(
                                    (t) => t.clmc == '收款账户确认书'
                                )[0].id,
                                clmc: '收款账户确认书',
                            },
                        }
                    )
                    .then((res) => {
                        this.allDsrs[index].skzhUnSave = false
                        this.allDsrs[index].skzhxxId = res.data.id
                        let wjs = this.sscls.filter(
                            (t) => t.clmc == '收款账户确认书'
                        )[0].wjs
                        if (!wjs) {
                            this.sscls.filter(
                                (t) => t.clmc == '收款账户确认书'
                            )[0].wjs = []
                        }
                        this.sscls
                            .filter((t) => t.clmc == '收款账户确认书')[0]
                            .wjs.push(res.data)
                    })
            }
            this.savedDsrs = JSON.parse(JSON.stringify(this.allDsrs))
            this.$forceUpdate()
        },
        async delPng(id, dsr) {
            let sscl = JSON.parse(
                JSON.stringify(
                    this.sscls.filter((t) => t.clmc == '收款账户确认书')[0]
                )
            )
            if (!dsr.skzhUnSave) {
                let body = {
                    cllx: sscl.cllx,
                    clmc: sscl.clmc,
                    deleteUrl: '/yzw-zxfw-lafw/api/v3/layy/ssclfj/delete',
                    fyId: this.$store.getters.fyId,
                    id: id,
                    layyid: this.$store.getters.layyId,
                    path: 'layy',
                    ssclid: sscl.id,
                    ssryId: dsr.id,
                    ywlx: null,
                    wjs: sscl.wjs.filter((t) => t.id == id),
                }
                this.$rpa.dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ssclfj/delete',
                    {
                        method: 'POST',
                        body: body,
                    }
                )
            }
            dsr.skzhUnSave = false
            dsr.skzhxxId = null
        },
        async saveDlr(dlrInfo) {
            if (!dlrInfo) {
                return
            }
            let index = this.dlrs.indexOf(dlrInfo)
            let savedDlr = this.savedDlrs[index]
            if (
                savedDlr &&
                JSON.stringify(savedDlr) === JSON.stringify(dlrInfo)
            ) {
                return
            }
            let res = await this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/dlr',
                {
                    method: dlrInfo.id ? 'PATCH' : 'POST',
                    body: {
                        fyId: this.$store.getters.fyId,
                        ...dlrInfo,
                    },
                }
            )
            if (!this.dlrs[index].id) this.dlrs[index].id = res.data
            this.savedDlrs = JSON.parse(JSON.stringify(this.dlrs))
            this.$forceUpdate()
        },
        async delDsr() {
            let id = this.allDsrs[this.activeDsr].id
            if (id) {
                await this.$messageConfirm({
                    message: '确认删除此当事人？',
                    confirmText: '确认',
                })
                await this.$rpa.dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/dsr/${this.layyid}/${id}`,
                    {
                        method: 'DELETE',
                    }
                )
            }
            let nowIndex = this.dsrs.indexOf(this.allDsrs[this.activeDsr])
            this.allDsrs[this.activeDsr] = {}
            this.savedDsrs = JSON.parse(JSON.stringify(this.allDsrs))
            if (this.dsrs && this.dsrs.length > 0) {
                if (this.dsrs[nowIndex]) {
                    this.activeDsr = this.dsrs[nowIndex].index
                } else {
                    this.activeDsr = this.dsrs[nowIndex - 1].index
                }
            } else {
                this.activeDsr = null
            }

            this.$forceUpdate()
        },

        async delDlr() {
            let id = this.activeDlrObj.id
            if (id) {
                await this.$messageConfirm({
                    message: '确认删除此代理人？',
                    confirmText: '确认',
                })
                await this.$rpa.dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/dlr/${this.layyid}/${id}`,
                    {
                        method: 'DELETE',
                    }
                )
            }
            this.dlrs.splice(this.activeDlr, 1)
            let item = this.dlrs[this.activeDlr]
            // 删除后修改activeDlr的位置
            if (!item) {
                item = this.dlrs[this.activeDlr - 1]
                if (item) {
                    this.activeDlr = this.activeDlr - 1
                } else {
                    this.activeDlr = null
                }
            }
            this.savedDsrs = JSON.parse(JSON.stringify(this.allDsrs))
            this.$forceUpdate()
        },

        addDlr() {
            this.dlrs.push({
                fyId: this.$store.getters.fyId,
                layyid: this.layyid,
            })
            this.activeDlr = this.dlrs.length - 1
        },
        confirmAddress(address) {
            if (address) {
                this.allDsrs[this.activeDsr].skzhxxId = address.bh
                this.allDsrs[this.activeDsr].skzhUnSave = true
                this.allDsrs[this.activeDsr].url = address.wjyl
            } else {
                this.allDsrs[this.activeDsr].skzhxxId = null
            }
            this.addressModalOpen = false
        },
        openAddressModal() {
            if (this.allDsrs[this.activeDsr].skzhxxId) {
                this.$convertPdfUrlToBase64Image(
                    this.allDsrs[this.activeDsr].url
                ).then((base64) => {
                    this.showImagePreview(base64)
                })
                return
            }
            this.addressModalOpen = true
        },
        // 打开图片预览
        showImagePreview(base64Image) {
            // 验证base64格式（简单验证）
            if (
                typeof base64Image === 'string' &&
                base64Image.startsWith('data:image')
            ) {
                this.previewImage = base64Image
                this.showPreview = true
                // 禁止背景滚动
                document.body.style.overflow = 'hidden'
            } else {
                console.error('无效的base64图片格式')
            }
        },
        // 关闭预览
        closePreview() {
            this.showPreview = false
            // 恢复背景滚动
            document.body.style.overflow = ''
        },
    },
}
</script>
<style scoped>
.left_con {
    width: 218px;
    margin-right: 40px;
    align-items: center;
}

.left_con .tab {
    width: 100%;
    height: 20%;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    justify-content: center;
    align-items: center;
}

.left_con > .tab:not(:last-child) {
    border-bottom: 1px solid #a1b1c5;
}

.tab.require::before {
    content: '*';
    margin-right: 4px;
}

.tab.disabled {
    color: #a1b1c5;
}

.tab.undone {
    color: #c63131;
}

.tab.active {
    background: #3173c6;
    color: #fff;
}
.tab.undone.active {
    color: #f9a4a4;
}
.right_con {
    width: 1484px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    position: relative;
    flex-direction: column;
}

.right_con .con_head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 72px;
    width: 100%;
}

.right_con .con_body {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
}

.empty_body {
    width: 1482px;
    height: 491px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 52px;
    color: #a1b1c5 !important;
}

.right_tabs {
    display: flex;
    align-items: center;
    overflow: auto;
    flex-wrap: nowrap;
}
.right_tabs::-webkit-scrollbar {
    display: none;
}

.right_tabs > * {
    flex: none; /* 完全禁用伸缩（等价于 flex: 0 0 auto） */
}

.right_tab {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #a1b1c5;
    margin-right: 30px;
    height: 48px;
}

.right_tab span {
    border-bottom: 1px solid #a1b1c5;
}

.right_tab.active {
    color: #3173c6;
}
.right_tab.active span {
    border-bottom: 1px solid #3173c6;
}
.right_tab.undone::after {
    content: '待完善';
    margin-right: 4px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #78808a;
    margin-left: 4px;
}
.right_con input {
    outline: none;
    border: none;
    width: calc(100% - 10px);
    height: 100%;
    text-indent: 10px;
    font-size: 24px;
}

.r_btns {
    display: flex;
}
.r_btn {
    width: 150px;
    height: 52px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #3173c6;
    border: 1px solid #f1f5ff;
}

.r_btn:last-child {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.r_btn:first-child {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}
.account_png {
    height: 135px;
    width: 100%;
    border: 1px solid #a1b1c5;
    display: flex;
    align-items: center;
    padding-left: 30px;
}
.addPng {
    width: 270px;
    height: 60px;
    background: #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
}

.delPng {
    font-size: 24px;
    color: #3173c6;
    margin-left: 20px;
}

.delDsr {
    position: absolute;
    right: -14px;
    bottom: -9px;
}
</style>
<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px 20px 0 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}

.border {
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.ip_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}

.btns {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
}
</style>