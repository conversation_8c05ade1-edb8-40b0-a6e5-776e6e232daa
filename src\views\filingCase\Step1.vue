<template>
    <div>
        <Container>
            <template #head>
                <div class="ip_head">民事一审
                    <Steps :active="0" />
                </div>

            </template>
            <div class="con">
                {{ $store.getters.lafsName }}-{{ $store.getters.applyCaseTypeName}}
                <div class="border">
                    <div class="ip_title">欢迎使用在线服务网在线立案，在线立案操作前请认真阅读以下诉讼权利义务告知:</div>
                    <div class="content">
                        {{layerText}}
                    </div>
                </div>
                <div class="btns">
                    <div class="checkBox" @click="isChecked = !isChecked">
                        <img src="@/assets/checked.png" v-if="isChecked" />
                        <img src="@/assets/unchecked.png" v-else />
                        已阅读同意立案须知内容
                    </div>
                    <div class="ip_btn" @click="$router.go(-1)">上一步</div>
                    <div class="ip_btn deep_btn" v-if="isChecked" @click="goNext">下一步</div>
                </div>
            </div>
        </Container>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import Steps from '@/components/Steps.vue'
import { CASE_TYPE } from '@/utils/bizConstant'
export default {
    name: 'UserType',
    components: {
        Container,
        Steps,
    },
    data() {
        return {
            caseType: CASE_TYPE,
            isChecked: false,
            layerText:
                ' 一、您可以在"在线服务平台"申请在线立案，在线诉讼活动与线下诉讼活动具有同等法律效力，线上电子签名与线下签名有同等效力。\n二、当事人或诉讼代理人应诚信诉讼。同意进入本平台后在本平台上发表的所有文字、语音、视频、图片等均视为本人操作，由本人承担相应的法律责任。\n三、当事人及其诉讼代理人主动选择进入本平台参与诉讼活动的，相应诉讼环节可以直接在线进行。在线立案申请时如果您通过本平台制作的送达地址确认书，默认同意通过"人民法院在线平台"进行电子送达。\n四、当事人及其诉讼代理人可通过本平台在线完成立案、调解、证据交换、询问、庭审、送达等全部或者部分诉讼环节。\n五、当事人及其诉讼代理人应当遵守数据安全和个人信息保护的相关法律法规，不得违法违规披露、传播和使用在线诉讼数据信息。\n六、当事人及其诉讼代理人应保持手机畅通，若在诉讼过程中发生手机遗失、微信被盗等特殊情形时，应及时告知承办人并采取补救措施，在此期间所产生的一切法律后果均由当事人本人承担。',
        }
    },
    methods: {
        async excuteLayy() {
            let param = {
                ajcx:
                    this.$store.getters.caseType == this.caseType.SQTJ
                        ? 'tj'
                        : 'sp', // 调解/审判
                ajlx: this.$store.getters.caseType,
                lafs: this.$store.getters.lafs || '1',
                tjcgsqsfqr: '1',
                fyid: this.$store.getters.fyId,
                sqrsf: this.$store.getters.applyUserType || '11800010-1', // 使用store中的申请人类型
                ajlb:
                    this.$store.getters.caseType == this.caseType.SQTJ
                        ? 'tj'
                        : 'sp', // 调解/审判
                pcSqrLx: '',
                sqrlx:
                    1 == localStorage.getItem('role')
                        ? '11800010-1' == this.$store.getters.applyUserType
                            ? '11800011-2'
                            : '11800011-3'
                        : '11800011-1',
                sfid: '450000', // 省份
                ftmc: '',
                sfzscq: this.$store.getters.isIp || '1501_000010-2', // 是否知识产权，使用store中的值
            }
            let layyInfoRes = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy`,
                {
                    method: 'POST',
                    body: param,
                }
            )
            if (layyInfoRes.code === 200 && layyInfoRes.data) {
                let layyId = layyInfoRes.data
                // 将立案预约ID存入store
                this.$store.dispatch('layy/setLayyId', layyId)
                let res = await this.$rpa.dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.$store.getters.layyId}/0`,
                    { method: 'GET' }
                )

                if (res.code === 200 && res.data) {
                    // 将立案预约信息存入store
                    this.$store.dispatch('layy/setLayyInfo', res.data)
                    this.$store.dispatch('layy/setLafs', res.data.layy.lafs)
                }
                return true
            } else {
                this.$messageConfirm({
                    message: '立案预约失败',
                    confirmText: '确定',
                })
                return false
            }
        },
        async goNext() {
            // 检查是否勾选了立案须知
            if (!this.isChecked) {
                this.$messageConfirm({
                    message: '请先阅读并勾选同意立案须知内容',
                    confirmText: '确定',
                })
                return
            }

            // 开始立案
            const result = await this.excuteLayy()
            if (result) {
                this.$router.push('/step2')
            }
        },
    },
}
</script>

<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 20px 30px;
}

.ip_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}

.ip_title {
    width: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 42px;
    color: #333333;
}

.btns {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
}
.content {
    white-space: pre-line;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 30px;
    color: #333333;
    line-height: 42px;
    margin-top: 12px;
}

.checkBox {
    display: flex;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    align-items: center;
    position: absolute;
    left: 0;
    transform: translateY(50%);
}

.checkBox img {
    height: 48px;
    width: 48px;
    margin-right: 8px;
}
</style>