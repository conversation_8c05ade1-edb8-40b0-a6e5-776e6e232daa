<template>
    <div class="dq_form_item" :style="{width: span * width + 'px'}"
        :class="(required? 'required': '') + (formClass? ' ' + formClass: '') +(disabled ? ' disabled': '')">
        <div class="dq_label" :class="label.length > 7 ? 'dq_label_long' : ''">
            {{label}}
        </div>
        <div class="dq_content">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'FormItem',
    props: {
        label: {
            type: String,
            default: '',
        },
        required: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        span: {
            type: Number,
            default: 1,
        },
        formClass: {
            type: String,
        },
    },
    data() {
        return {
            width: 494,
        }
    },
}
</script>

<style scoped>
.dq_form_item {
    display: flex;
    height: 74px;
    border: 1px solid #a1b1c5;
}
.dq_label {
    width: 218px;
    height: 72px;
    background: #f1f5ff;
    border-right: 1px solid #a1b1c5;
    font-family: Source <PERSON>, Source Han <PERSON>;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    line-height: 72px;
    text-align: right;
    padding-right: 10px;
    white-space: wrap;
}

.dq_label_long {
    font-size: 22px;
}

.required .dq_label::before {
    content: '*';
    color: rgba(198, 49, 49, 1);
    margin-right: 4px;
}
.dq_content {
    flex: 1;
    height: 72px;
    background: #fff;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    max-width: calc(100% - 218px);
    overflow: hidden;
}
</style>