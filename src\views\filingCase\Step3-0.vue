<template>
    <div>
        <Container :timeout="timeout">
            <template #head>
                <div class="ip_head">民事一审
                    <Steps :active="2" />
                </div>

            </template>
            <div class="con">
                诉状识别立案-诉状扫描
                <div class="border">
                    <template v-if="!isScanning && imgList.length == 0">
                        <img src="@/assets/qsz_scanner.png" style="width: 500px;height: 388px;" alt="">
                        <div class="ip_title">将起诉状反转扣入扫描口</div>
                    </template>
                    <template v-else-if="isScanning">
                        <div class="scaning-page-container">
                            <div style="width:340px;height:352px;">
                                <div>
                                    <img src="@/assets/ai_logo_header.png" alt="" class="fading-logo-header">
                                </div>
                                <div>
                                    <img src="@/assets/ai_logo_body.png" alt="">
                                </div>
                            </div>
                            <div class="scanningText">
                                扫描中，已扫描<span class="imgSize">{{ imgList.length }}</span>页
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="pdf_con">
                            <div class="pdf_scroll" ref="pdfScroll">
                                <img v-for="item,index in imgList" :key="index" :src="item.imgBase64" alt="" />
                            </div>
                            <div class="pdf_page_bar">
                                <img src='@/assets/pageUp.png' @click="scroll('pdfScroll',-1)" />
                                <img src='@/assets/pageDown.png' @click="scroll('pdfScroll',1)" />
                            </div>
                        </div>
                    </template>
                </div>
                <div class="btns">
                    <div class="ip_btn" v-if="!isScanning" @click="goBack">上一步</div>
                    <div class="ip_btn deep_btn" v-if="!isScanning && imgList.length == 0" @click="startScan">开始扫描</div>
                    <div class="ip_btn deep_btn" v-if="!isScanning && imgList.length > 0" @click="goNext">下一步</div>
                </div>
            </div>
        </Container>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import Steps from '@/components/Steps.vue'
import { DQScanistor } from 'dq-eqc'
export default {
    name: 'UserType',
    components: {
        Container,
        Steps,
    },
    data() {
        return {
            isScanning: false,
            // Base64图片列表
            imgList: [],
            scanIndex: 0,
            scannerEndFlag: false,
        }
    },
    computed: {
        timeout() {
            return this.isScanning ? 0 : 120
        },
    },
    methods: {
        startScan() {
            DQScanistor.open((res) => {
                // 开启扫描仪成功
                if (res.code == '201') {
                    this.isScanning = true
                    this.scanIndex = 0
                    this.imgList = []
                }
                // 接收扫描仪Base64图片
                if (res.code == '200') {
                    let originImgBase64 = res.result
                    // 添加图片到列表
                    this.imgList.push({
                        index: this.scanIndex,
                        imgBase64: originImgBase64,
                    })
                    this.scanIndex++
                }
                // 扫描结束
                if (res.code == '202') {
                    this.scannerEndFlag = true
                    // 检查所有OCR识别是否完成
                    this.checkAllRecognized()
                }
                // 扫描仪卡纸异常
                if (res.code == '203') {
                    this.$messageConfirm({
                        message: '扫描仪卡纸，请将材料拿出重新扫码',
                        confirmText: '重新扫描',
                    }).then(() => {
                        this.isScanning = false
                    })
                }
                // 扫描仪异常
                if (res.code == '204') {
                    this.$messageConfirm({
                        message: '扫描仪异常，请联系管理人员',
                        confirmText: '确定',
                    }).then(() => {
                        this.isScanning = false
                    })
                }
                // 扫描仪无纸异常
                if (res.code == '205') {
                    this.$messageConfirm({
                        message: '扫描仪无纸，请将材料放入扫描仪',
                        confirmText: '确定',
                    }).then(() => {
                        this.isScanning = false
                    })
                }
            })
        },
        // 检查是否所有图片都已识别
        checkAllRecognized() {
            // 如果已设置扫描结束标志
            if (this.scannerEndFlag) {
                // 扫描完成，结束扫描状态
                this.isScanning = false
                this.scannerEndFlag = false // 重置标志
            }
        },
        clearScanner() {
            this.imgList = []
            this.isScanning = false
            this.scanIndex = 0
            this.scannerEndFlag = false
        },
        goBack() {
            if (this.imgList.length > 0) {
                this.clearScanner()
                return
            }
            this.$router.push('/step2')
        },
        async goNext() {
            this.$store.dispatch('loading/setLoading', true)
            try{
                // 使用Promise.all等待所有上传完成
                const uploadPromises = this.imgList.map((item) =>
                    this.uploadImage(item)
                )
                const results = await Promise.all(uploadPromises)

                // 检查是否所有上传都成功
                if (results.includes(false)) {
                    this.$messageConfirm({
                        message: '部分材料上传失败，请重试',
                        confirmText: '确定',
                    })
                    return
                }

                let res = await this.$rpa.dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ocr?layyId=${this.$store.getters.layyId}`,
                    {
                        method: 'GET',
                    },
                    false
                )
                if (res.code == 200) {
                    this.$router.push('/step3')
                }
            } finally {
                this.$store.dispatch('loading/setLoading', true)
            }

        },
        scroll(ref, count) {
            let dom = this.$refs[ref]
            dom.scrollBy({
                top: dom.offsetHeight * 0.5 * count,
                behavior: 'smooth',
            })
        },

        // 图片上传
        async uploadImage(img) {
            try {
                let ssclTypeList = this.$store.getters.ajlxInfo.sscl
                // 1. 获取签名
                let param1 = {
                    cllx: ssclTypeList.find((item) => item.name == '起诉状')
                        .cllx,
                    fydm: this.$store.getters.fyId,
                    ext: '.jpg',
                    path: 'layy',
                }
                let res1 = await this.$rpa.dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-ajfw/api/v1/file/upload/signature`,
                    {
                        method: 'POST',
                        body: param1,
                    }, false
                )
                if (res1.code != 200) {
                    console.error('图片上传获取签名失败')
                    return false
                }

                // 2. 前置上传
                let signatureInfo = res1.data
                // 创建FormData对象
                const formData = new FormData()
                // 添加签名相关字段
                formData.append('key', signatureInfo.storeAs)
                formData.append('x-oss-security-token', signatureInfo.token)
                formData.append('policy', signatureInfo.policy)
                formData.append('OSSAccessKeyId', signatureInfo.ossaccessKeyId)
                formData.append('success_action_status', '200')
                formData.append('signature', signatureInfo.signature)
                // 将Base64图片转换为Blob对象
                const byteString = atob(img.imgBase64.split(',')[1])
                const mimeString = img.imgBase64
                    .split(',')[0]
                    .split(':')[1]
                    .split(';')[0]
                const ab = new ArrayBuffer(byteString.length)
                const ia = new Uint8Array(ab)
                for (let i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i)
                }
                const blob = new Blob([ab], { type: mimeString })
                // 添加文件
                formData.append('file', blob)
                // 发送请求
                const res2 = await this.$rpa.dealFetch(
                    'https://zxfy2-oss.oss-cn-north-2-gov-1.aliyuncs.com/',
                    {
                        method: 'POST',
                        body: formData,
                    }, false
                )
                // 检查上传结果
                if (!res2.success) {
                    this.$messageConfirm({ message: `图片上传失败` })
                    return false
                }

                // 生成文件名: 如果是第一个文件，则不显示序号；从第二个文件开始显示序号
                const displayIndex = img.index > 0 ? img.index + 1 : ''
                const fileName = `起诉状${displayIndex}.jpg`

                // 3. 上传材料
                let param3 = {
                    wjbh: signatureInfo.storeAs,
                    layyid: this.$store.getters.layyId,
                    fyId: this.$store.getters.fyId,
                    xh: img.index + 1, // 序号从1开始
                    wjmc: fileName,
                    path: signatureInfo.storeAs,
                    ssclid: this.getSsclId(param1.cllx),
                    cllx: param1.cllx,
                    clmc: '起诉状',
                    bccl: null,
                    name: fileName,
                    extname: 'jpg',
                    url: 'blob:https://zxfw.court.gov.cn/123', // 可以随便传
                }

                let res3 = await this.$rpa.dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ssclfj`,
                    {
                        method: 'POST',
                        body: param3,
                    }, false
                )
                if (res3.code !== 200) {
                    console.error('起诉状材料信息上传失败')
                    return false
                }

                return true
            } catch (error) {
                console.error('起诉状上传过程出错:', error)
                return false
            }
        },
        // 安全获取ssclid的方法
        getSsclId(cllx) {
            // 检查layyInfo是否存在
            if (!this.$store.getters.layyInfo) {
                console.error('layyInfo为空，请先获取立案预约信息')
                // 尝试重新获取立案预约信息
                this.getLayyDetail()
                return ''
            }

            // 检查sscls是否存在
            if (
                !this.$store.getters.layyInfo.sscls ||
                !Array.isArray(this.$store.getters.layyInfo.sscls)
            ) {
                console.error('sscls不存在或不是数组')
                return ''
            }

            // 查找匹配的项
            const matchedItem = this.$store.getters.layyInfo.sscls.find(
                (item) => item.cllx == cllx
            )
            return matchedItem ? matchedItem.id : ''
        },
        // 获取立案预约信息
        async getLayyDetail() {
            let layyInfoRes = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.$store.getters.layyId}/0`,
                { method: 'GET' }
            )

            if (layyInfoRes.code === 200 && layyInfoRes.data) {
                // 将立案预约信息存入store
                this.$store.dispatch('layy/setLayyInfo', layyInfoRes.data)
                return true
            }
            return false
        },
    },
    created() {
        let layyInfo = this.$store.getters.layyInfo
        if (
            !!layyInfo &&
            layyInfo.sscls.some(
                (item) =>
                    item.clmc == '起诉状' && !!item.wjs && item.wjs.length > 0
            )
        ) {
            this.$router.push('/step3')
        }
    },
}
</script>

<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 20px 30px;
}

.ip_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}

.ip_title {
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    margin-top: 30px;
}

.btns {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
}
.content {
    white-space: pre-line;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 30px;
    color: #333333;
    line-height: 42px;
    margin-top: 12px;
}

.checkBox {
    display: flex;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    align-items: center;
    position: absolute;
    left: 0;
    transform: translateY(50%);
}

.checkBox img {
    height: 48px;
    width: 48px;
    margin-right: 8px;
}

/* 扫描中动画和文字相关样式 */
.scaning-page-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.scanningText {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    height: 48px;
    line-height: 48px;
    margin-top: 30px;
}

.imgSize {
    font-size: 48px;
    color: #0b7cff;
    margin: 0 10px;
}

.fading-logo-header {
    float: right;
    animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }
}

.pdf_con {
    width: 1354px;
    height: 500px;
    background: #dbe6f1;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    padding: 20px;
    margin-top: 20px;
    position: relative;
}

.pdf_scroll {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.pdf_scroll::-webkit-scrollbar {
    display: none;
}

.pdf_scroll img {
    width: 100%;
}

.pdf_page_bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 170px;
    position: absolute;
    right: -60px;
    top: 197px;
}
</style>