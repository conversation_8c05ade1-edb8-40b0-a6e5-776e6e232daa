import { createApp } from 'vue'
import Antd from 'ant-design-vue';
import { message } from 'ant-design-vue';
import App from './App.vue'
import router from './router'
import store from './store'
import rpa from './utils/rpa'
import messageConfirm from './plugins/messageConfirm'
import { createWebsocketInstance } from 'dq-eqc'
import * as request from './utils/request'
import './styles/font.css'
import './styles/main.css'
import 'ant-design-vue/dist/reset.css';
import * as pdfjsLib from 'pdfjs-dist'

// 创建websocket插件
const websocketPlugin = {
    install: (app) => {
        // 初始化为null
        app.config.globalProperties.$websocketInstance = null

        // 提供设置websocket实例的方法
        app.config.globalProperties.$setWebsocketInstance = (instance) => {
            app.config.globalProperties.$websocketInstance = instance
        }
    }
}

const app = createApp(App).use(store).use(router)
app.config.globalProperties.$rpa = rpa
app.config.globalProperties.$message = message
app.config.globalProperties.$http = request // 注册HTTP请求工具

function getText(list, value) {
    let item = list.find((t) => t.value == value)
    return item ? item.text : '请选择'
}
app.config.globalProperties.$getText = getText
// window.addEventListener('unhandledrejection', (event) => {
//     console.error('[未处理的Promise错误]', event.reason)
//     // 阻止浏览器默认的错误打印
//     event.preventDefault()
//     event.stopImmediatePropagation() // 阻止其他监听器处理
// })
function needScroll(dom) {
    return dom.scrollHeight > dom.clientHeight
}
app.config.globalProperties.$needScroll = needScroll

pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
    `./utils/pdf.worker.min.js`,
    import.meta.url
).toString()

async function convertPdfUrlToBase64Image(url) {
    // 1. 加载PDF文档
    const loadingTask = pdfjsLib.getDocument(url)
    const pdf = await loadingTask.promise
    const totalPages = pdf.numPages

    // 2. 创建离屏Canvas渲染所有页面
    const pagePromises = []
    for (let i = 1; i <= totalPages; i++) {
        pagePromises.push(
            pdf.getPage(i).then((page) => renderPage(page, 2))
        )
    }

    // 3. 并行渲染所有页面
    const pages = await Promise.all(pagePromises)

    // 4. 计算总高度并创建最终Canvas
    const totalHeight = pages.reduce(
        (sum, canvas) => sum + canvas.height,
        0
    )
    const maxWidth = Math.max(...pages.map((c) => c.width))

    const finalCanvas = document.createElement('canvas')
    finalCanvas.width = maxWidth
    finalCanvas.height = totalHeight
    const ctx = finalCanvas.getContext('2d')

    // 5. 拼接所有页面
    let yPos = 0
    pages.forEach((canvas) => {
        ctx.drawImage(canvas, 0, yPos)
        yPos += canvas.height
    })

    // 6. 导出为图片
    return finalCanvas.toDataURL('image/png')
}

// 单页渲染函数
async function renderPage(page, scale) {
    const viewport = page.getViewport({ scale })
    const canvas = document.createElement('canvas')
    canvas.width = viewport.width
    canvas.height = viewport.height

    await page.render({
        canvasContext: canvas.getContext('2d'),
        viewport,
    }).promise

    return canvas
}

app.config.globalProperties.$convertPdfUrlToBase64Image = convertPdfUrlToBase64Image

// 统一获取立案预约信息
// 获取立案预约信息
async function getLayyDetail() {
    let layyInfoRes = await rpa.dealFetch(
        `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.$store.getters.layyId}/0`,
        { method: 'GET' }
    )

    if (layyInfoRes.code === 200 && layyInfoRes.data) {
        // 将立案预约信息存入store
        store.dispatch('layy/setLayyInfo', layyInfoRes.data)
    }
    return layyInfoRes
}

app.config.globalProperties.$getLayyDetail = getLayyDetail

// 使用websocket插件
app.use(websocketPlugin)
app.use(Antd)
app.use(messageConfirm)
app.mount('#app')