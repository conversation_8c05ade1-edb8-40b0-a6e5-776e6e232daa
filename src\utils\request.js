import axios from 'axios'
import { message } from 'ant-design-vue'

// 创建axios实例
const service = axios.create({
  baseURL: 'http://localhost:8080', // API基础URL
  timeout: 600000, // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    // 这里可以根据后端返回的状态码进行判断
    if (res.code && res.code !== 200) {
      message.error(res.message || '请求失败')
      return Promise.reject(new Error(res.message || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误:', error)
    const { response } = error
    let errorMsg = '请求失败'
    
    if (response && response.status) {
      switch (response.status) {
        case 400:
          errorMsg = '请求错误'
          break
        case 401:
          errorMsg = '未授权，请重新登录'
          // 可以在这里处理登出逻辑
          break
        case 403:
          errorMsg = '拒绝访问'
          break
        case 404:
          errorMsg = '请求资源不存在'
          break
        case 500:
          errorMsg = '服务器内部错误'
          break
        default:
          errorMsg = `连接错误 ${response.status}`
      }
    } else {
      errorMsg = '网络连接异常'
    }
    
    message.error(errorMsg)
    return Promise.reject(error)
  }
)

/**
 * GET请求方法
 * @param {String} url 请求URL
 * @param {Object} params 请求参数
 * @param {Object} config 自定义配置
 * @returns {Promise} Promise
 */
export function get(url, params = {}, config = {}) {
  return service({
    url,
    method: 'get',
    params,
    ...config
  })
}

/**
 * POST请求方法
 * @param {String} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} config 自定义配置
 * @returns {Promise} Promise
 */
export function post(url, data = {}, config = {}) {
  return service({
    url,
    method: 'post',
    data,
    ...config
  })
}

/**
 * PUT请求方法
 * @param {String} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} config 自定义配置
 * @returns {Promise} Promise
 */
export function put(url, data = {}, config = {}) {
  return service({
    url,
    method: 'put',
    data,
    ...config
  })
}

/**
 * DELETE请求方法
 * @param {String} url 请求URL
 * @param {Object} params 请求参数
 * @param {Object} config 自定义配置
 * @returns {Promise} Promise
 */
export function del(url, params = {}, config = {}) {
  return service({
    url,
    method: 'delete',
    params,
    ...config
  })
}

// 导出axios实例
export default service 