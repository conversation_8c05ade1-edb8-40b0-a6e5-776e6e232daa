{"name": "dqist_zxfw_host_h5", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"ant-design-vue": "^4.2.6", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dq-eqc": "^1.0.36", "html2canvas": "^1.4.1", "pdfjs-dist": "^2.16.105", "qrcode": "^1.5.4", "vue": "^3.2.13", "vue-native-websocket": "^2.0.15", "vue-router": "^4.0.3", "vuex": "^4.0.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}