<template>
    <div class="app-container">
        <TheHeader v-if="shouldShowHeader" />
        <div class="main-content-wrapper">
            <router-view class="main-content" />
        </div>
        <!-- 全屏 loading 覆盖整个应用 -->
        <div v-if="loading" class="global-loading">
            <a-spin size="large" />
        </div>
        <div class="footer">
            <img src="@/assets/logo.png" alt="Logo" />
        </div>
    </div>
</template>

<script>
import TheHeader from './components/TheHeader.vue'
import { createWebsocketInstance, DQLog } from 'dq-eqc'
import { logStatic } from '@/api/log'
import { mapState } from 'vuex'
import { Spin } from 'ant-design-vue'
import { WEBSOCKET_URL } from '@/config'

export default {
    name: 'App',
    components: {
        TheHeader,
        ASpin: Spin,
    },
    data() {
        return {
            courtName: '江苏省苏州市工业园区人民法院',
            deviceName: '桌面自助扫描终端',

            websocket: null,
            websocketUrl: null,
        }
    },
    computed: {
        ...mapState({
            loading: (state) => state.loading.loading,
        }),
        shouldShowHeader() {
            return this.$route.meta.showHeader !== false
        },
    },
    mounted() {
        this.$store.dispatch('loading/setLoading', false)
        this.websocketUrl = WEBSOCKET_URL
        this.initWebSocket()
        /*** 用户操作记录二 start***/
        let self = this
        document
            .getElementById('app')
            .addEventListener('click', function (item) {
                if (!item.target.dataset.click) {
                    return
                }

                let clickDescription = item.target.dataset.click
                let route = self.$route
                let timestamp = new Date().getTime()
                let pageName = route.meta.description
                    ? route.meta.description
                    : null
                let userInfo = self.$store.getters.currenUserInfo //空
                // 用户姓名
                let personName = ''
                if (userInfo) {
                    personName = userInfo.name ? userInfo.name : ''
                }
                let leavePage = {
                    personname: personName,
                    cardnumber: userInfo && userInfo.zjhm,
                    pagename: pageName,
                    pageurl: route.fullPath,
                    clickdescription: clickDescription,
                    logdate: timestamp,
                }
                logStatic(leavePage).then((result) => {})
            })
        /*** 用户操作记录二 end***/
        /**
         * 增强console.log
         */
        this.enhanceConsoleLog()
    },
    methods: {
        /**
         * 对console.log进行增强
         */
        enhanceConsoleLog() {
            let originalLog = console.log
            console.log = function (...message) {
                originalLog(...message)
                try {
                    DQLog.log({
                        message: 'log =====>' + JSON.stringify(message),
                        logName: 'log',
                    })
                } catch (e) {}
            }

            let originalError = console.error
            console.error = function (...message) {
                let args = Array.prototype.slice.call(message)
                originalError(message)
                try {
                    DQLog.log({
                        message: 'error =====>' + JSON.stringify(args),
                        logName: 'log',
                    })
                } catch (e) {}
            }
        },
        /**
         * 初始化websocket连接
         */
        initWebSocket() {
            this.websocket = null
            if (!this.websocketUrl) {
                return
            }
            createWebsocketInstance(this.websocketUrl)
                .then((websocketInstance) => {
                    // 成功建立WebSocket连接
                    this.websocket = websocketInstance
                    // 只设置全局websocket实例
                    this.$setWebsocketInstance(websocketInstance)
                    this.$message.success('websocket已连接')

                    this.websocket.addEventListener('close', () => {
                        this.websocket = null
                        // 只清空全局websocket实例
                        this.$setWebsocketInstance(null)
                        this.$message.warning('websocket连接已断开')
                    })
                })
                .catch((error) => {
                    console.error(error)
                    // 处理WebSocket连接错误
                    this.$message.error('websocket连接已断开')
                })
        },
    },
    provide() {
        return {
            courtName: this.courtName,
            deviceName: this.deviceName,
        }
    },
}
</script>

<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body,
html {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
}

.app-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content-wrapper {
    position: relative;
    height: 100%;
    background: url('@/assets/bg.png') no-repeat center center fixed;
}

.main-content {
    height: 100%;
}

.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    background: rgba(19, 19, 19, 0.25);
    height: 50px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>

<style>
/* 移除自动填充的黄色背景 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
    transition: background-color 5000s ease-in-out 0s;
}
</style>