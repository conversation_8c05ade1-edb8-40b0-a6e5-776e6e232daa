<template>
    <header class="header">
        <div class="header-left">
            <img src="@/assets/header/court-small.png" />{{ $parent.courtName }}
            <div class="header-left-click-overlay" @click="handleLogoClick"></div>
        </div>
        <div class="header-right">
            <template v-if="userInfo && userInfo.username">
                <div class="header-user">
                    <img src="@/assets/header/avatar.png" />
                    <span>{{ userInfo.username }}</span>
                </div>
                <div class="header-home" @click="goHome" v-if="showGoHome"><img
                        src="@/assets/header/home.png" /><span>返回首页</span>
                </div>
                <div class="header-logout" @click="logout"><img src="@/assets/header/logout.png" /><span>退出登录</span>
                </div>
            </template>
            <div class="header-date-time">
                <div class="header-date">
                    <div>{{currentDate}} {{currentWeek}}</div>
                </div>
                <div class="header-time">{{ currentTime }}</div>
            </div>
        </div>
    </header>
</template>
  
<script>
import { DQSYSCTL } from 'dq-eqc'
export default {
    name: 'TheHeader',
    data() {
        return {
            currentTime: '',
            currentWeek: '',
            currentDate: '',
            timer: null,
            logoClickCount: 0,
            logoClickTimer: null,
        }
    },
    computed: {
        showGoHome() {
            return (
                this.$route.path != '/actionType' &&
                this.userInfo &&
                this.userInfo.username
            )
        },
        userInfo() {
            return this.$store.getters.currentUserInfo
        },
    },
    mounted() {
        this.updateTime()
        this.timer = setInterval(this.updateTime, 1000)
    },
    beforeUnmount() {
        clearInterval(this.timer)
        // 清理logo点击计时器
        if (this.logoClickTimer) {
            clearTimeout(this.logoClickTimer)
        }
    },
    methods: {
        updateTime() {
            const now = new Date()
            this.currentTime = now.toLocaleString('zh-CN', {
                // year: 'numeric',
                // month: '2-digit',
                // day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false,
            })
            // 日期2025-06-03
            this.currentDate = now
                .toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                })
                .replaceAll('/', '-')
            this.currentWeek = now.toLocaleDateString('zh-CN', {
                weekday: 'long',
            })
        },
        logout() {
            this.$router.push('/')
        },
        goHome() {
            this.$router.push('/actionType')
        },
        handleLogoClick() {
            // 每次点击补充一个1s的定时器 如果没有下一次触发则清空count，count累计到5次console.log
            if (this.logoClickTimer) {
                clearTimeout(this.logoClickTimer)
            }

            if (this.logoClickCount === 4) {
                DQSYSCTL.closeBrowser()
                this.logoClickCount = 0
            } else {
                this.logoClickTimer = setTimeout(() => {
                    this.logoClickCount = 0
                }, 1000)
                this.logoClickCount++
            }
        },
    },
}
</script>
  
  <style scoped>
.header {
    height: 124px;
    background: linear-gradient(180deg, #214181 0%, #0f215a 100%);
    font-family: Source Han Sans CN, Source Han Sans CN;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 50px;
}

.header-left,
.header-center,
.header-right {
    flex: 1;
    height: 100%;
}

.header-left {
    font-weight: 500;
    font-size: 40px;
    display: flex;
    align-items: center;
    position: relative;
}

.header-left-click-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 60px;
    height: 100%;
    background: transparent;
    z-index: 10001;
    cursor: pointer;
}

.header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 80px;
}

.header-user {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex-direction: column;
    margin-right: 60px;
    height: 80px;
}

.header-logout {
    width: 160px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex-direction: column;
    margin-right: 60px;
}
.header-user span,
.header-home span,
.header-logout span {
    font-size: 20px;
    height: 20px;
    line-height: 20px;
}

.header-home {
    width: 160px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex-direction: column;
    margin-right: 20px;
}

.header-date-time {
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-direction: column;
    height: 100%;
}
.header-date {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 20px;
    height: 20px;
}

.header-time {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 40px;
    height: 40px;
}
</style>