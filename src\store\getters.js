const getters = {
  currentUserInfo: state => state.user.currentUserInfo,
  fyId: state => state.layy.fyId,
  ajlxInfo: state => state.layy.ajlxInfo,
  caseType: state => state.layy.caseType,
  layyInfo: state => state.layy.layyInfo,
  layyId: state => state.layy.layyId,
  layyid: state => state.layy.layyId,
  lafs: state => state.layy.lafs,
  lafsName: state => {
    const lafs = (state.layy.layyInfo && state.layy.layyInfo.layy.lafs) || state.layy.lafs;
    return lafs == '1' ? '普通立案' : '诉状识别立案';
  },
  applyUserType: state => state.layy.applyUserType,
  applyCaseTypeName: state => {
    const userType = (state.layy.layyInfo && state.layy.layyInfo.layy.sqrsf) || state.layy.applyUserType;
    return userType == '11800010-1' ? '本人申请' : '为他人/组织申请';
  },
  isIp: state => state.layy.isIp
};
export default getters
