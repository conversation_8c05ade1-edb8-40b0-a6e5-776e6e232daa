.center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.dq_content>input {
    outline: none;
    border: none;
    height: 100%;
    width: 100%;
    font-family: Source <PERSON>, Source <PERSON> Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    text-indent: 10px;
}

.dq_content>input::placeholder {
    font-family: Source <PERSON>, Source <PERSON>;
    font-weight: 400;
    font-size: 24px;
    color: #78808A;
}

.dq_content>input:disabled {
    background: rgba(118, 118, 118, 0.2)
}

.dq_content .selectDiv {
    background: #dbe6f1;
    border-radius: 0px 0px 0px 0px;
    height: 100%;
    width: 100%;
    font-family: Source <PERSON>, Source <PERSON>N;
    font-weight: 400;
    font-size: 24px;
    color: #3173c6;
    overflow: hidden;
}

body {
    font-family: Source <PERSON>, Source <PERSON>N;
}

.ip_btn {
    width: 216px;
    height: 72px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #3173c6;
    font-family: Source <PERSON>, Source <PERSON>;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 12px;
}

.deep_btn {
    background: #3173c6 !important;
    color: #ffffff !important;
}

.ip_btn:active {
    transform: scale(0.98);
}


/* 蒙层样式 */
.preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* 图片容器 */
.preview-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    overflow: auto
}

/* 预览图片 */
.preview-image {
    max-width: 100%;
    display: block;
    border-radius: 4px;

}